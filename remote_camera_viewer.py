#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
远程摄像头查看器
通过SSH连接到远程机器，访问四个摄像头并在本地显示画面
"""

import cv2
import numpy as np
import threading
import time
import paramiko
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import queue
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RemoteCameraViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("远程摄像头查看器")
        self.root.geometry("1200x800")
        
        # 连接参数
        self.remote_ip = tk.StringVar(value="*************")
        self.username = tk.StringVar(value="username")
        self.password = tk.StringVar(value="password")
        
        # SSH连接
        self.ssh_client = None
        self.is_connected = False
        
        # 摄像头相关
        self.camera_streams = {}
        self.camera_threads = {}
        self.frame_queues = {}
        self.camera_labels = {}
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI界面"""
        # 连接配置框架
        config_frame = ttk.LabelFrame(self.root, text="连接配置", padding="10")
        config_frame.pack(fill="x", padx=10, pady=5)
        
        # IP地址输入
        ttk.Label(config_frame, text="远程IP:").grid(row=0, column=0, sticky="w", padx=5)
        ttk.Entry(config_frame, textvariable=self.remote_ip, width=20).grid(row=0, column=1, padx=5)
        
        # 用户名输入
        ttk.Label(config_frame, text="用户名:").grid(row=0, column=2, sticky="w", padx=5)
        ttk.Entry(config_frame, textvariable=self.username, width=15).grid(row=0, column=3, padx=5)
        
        # 密码输入
        ttk.Label(config_frame, text="密码:").grid(row=0, column=4, sticky="w", padx=5)
        ttk.Entry(config_frame, textvariable=self.password, width=15, show="*").grid(row=0, column=5, padx=5)
        
        # 连接按钮
        self.connect_btn = ttk.Button(config_frame, text="连接", command=self.connect_to_remote)
        self.connect_btn.grid(row=0, column=6, padx=10)
        
        # 状态标签
        self.status_label = ttk.Label(config_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=7, padx=10)
        
        # 摄像头控制框架
        control_frame = ttk.LabelFrame(self.root, text="摄像头控制", padding="10")
        control_frame.pack(fill="x", padx=10, pady=5)
        
        # 启动/停止按钮
        self.start_btn = ttk.Button(control_frame, text="启动摄像头", command=self.start_cameras, state="disabled")
        self.start_btn.pack(side="left", padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止摄像头", command=self.stop_cameras, state="disabled")
        self.stop_btn.pack(side="left", padx=5)
        
        # 摄像头显示框架
        camera_frame = ttk.LabelFrame(self.root, text="摄像头画面", padding="10")
        camera_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建2x2网格显示四个摄像头
        for i in range(4):
            row = i // 2
            col = i % 2
            
            # 摄像头标签框架
            cam_frame = ttk.LabelFrame(camera_frame, text=f"摄像头 {i+1}")
            cam_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
            
            # 摄像头画面标签
            cam_label = ttk.Label(cam_frame, text="摄像头未启动", anchor="center")
            cam_label.pack(fill="both", expand=True)
            
            self.camera_labels[i] = cam_label
            self.frame_queues[i] = queue.Queue(maxsize=2)
        
        # 配置网格权重
        camera_frame.grid_rowconfigure(0, weight=1)
        camera_frame.grid_rowconfigure(1, weight=1)
        camera_frame.grid_columnconfigure(0, weight=1)
        camera_frame.grid_columnconfigure(1, weight=1)
        
    def connect_to_remote(self):
        """连接到远程机器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接到远程机器
            self.ssh_client.connect(
                hostname=self.remote_ip.get(),
                username=self.username.get(),
                password=self.password.get(),
                timeout=10
            )
            
            self.is_connected = True
            self.status_label.config(text="已连接", foreground="green")
            self.connect_btn.config(text="断开连接", command=self.disconnect_from_remote)
            self.start_btn.config(state="normal")
            
            logger.info(f"成功连接到远程机器: {self.remote_ip.get()}")
            messagebox.showinfo("连接成功", f"已成功连接到 {self.remote_ip.get()}")
            
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            messagebox.showerror("连接失败", f"无法连接到远程机器:\n{str(e)}")
            
    def disconnect_from_remote(self):
        """断开远程连接"""
        try:
            self.stop_cameras()
            
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
                
            self.is_connected = False
            self.status_label.config(text="未连接", foreground="red")
            self.connect_btn.config(text="连接", command=self.connect_to_remote)
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="disabled")
            
            logger.info("已断开远程连接")
            
        except Exception as e:
            logger.error(f"断开连接时出错: {str(e)}")
            
    def start_cameras(self):
        """启动所有摄像头"""
        if not self.is_connected:
            messagebox.showerror("错误", "请先连接到远程机器")
            return
            
        try:
            # 启动四个摄像头线程
            for i in range(4):
                thread = threading.Thread(target=self.camera_thread, args=(i,), daemon=True)
                thread.start()
                self.camera_threads[i] = thread
                
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            
            # 启动显示更新线程
            self.display_thread = threading.Thread(target=self.update_display, daemon=True)
            self.display_thread.start()
            
            logger.info("所有摄像头已启动")
            
        except Exception as e:
            logger.error(f"启动摄像头失败: {str(e)}")
            messagebox.showerror("错误", f"启动摄像头失败:\n{str(e)}")
            
    def stop_cameras(self):
        """停止所有摄像头"""
        try:
            # 停止所有摄像头流
            for i in range(4):
                if i in self.camera_streams:
                    if self.camera_streams[i] is not None:
                        self.camera_streams[i].release()
                    del self.camera_streams[i]
                    
            self.camera_threads.clear()
            
            # 清空显示
            for i in range(4):
                self.camera_labels[i].config(image="", text="摄像头已停止")
                
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            
            logger.info("所有摄像头已停止")
            
        except Exception as e:
            logger.error(f"停止摄像头时出错: {str(e)}")
            
    def camera_thread(self, camera_id):
        """摄像头线程函数"""
        try:
            # 通过SSH端口转发访问远程摄像头
            # 这里假设远程机器的摄像头设备为 /dev/video0, /dev/video1, /dev/video2, /dev/video3
            local_port = 5000 + camera_id
            remote_device = f"/dev/video{camera_id}"
            
            # 在远程机器上启动摄像头流服务
            command = f"ffmpeg -f v4l2 -i {remote_device} -vcodec libx264 -preset ultrafast -tune zerolatency -f mpegts tcp://0.0.0.0:{local_port}"
            
            # 执行远程命令
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            
            # 等待一下让服务启动
            time.sleep(2)
            
            # 通过SSH隧道连接到远程流
            tunnel_command = f"ssh -L {local_port}:localhost:{local_port} {self.username.get()}@{self.remote_ip.get()}"
            
            # 使用OpenCV连接到本地端口
            stream_url = f"tcp://localhost:{local_port}"
            cap = cv2.VideoCapture(stream_url)
            
            if not cap.isOpened():
                # 如果TCP流不可用，尝试直接访问USB摄像头
                cap = cv2.VideoCapture(camera_id)
                
            self.camera_streams[camera_id] = cap
            
            while camera_id in self.camera_streams:
                ret, frame = cap.read()
                if ret:
                    # 调整帧大小
                    frame = cv2.resize(frame, (320, 240))
                    
                    # 将帧放入队列
                    if not self.frame_queues[camera_id].full():
                        self.frame_queues[camera_id].put(frame)
                else:
                    logger.warning(f"摄像头 {camera_id} 读取帧失败")
                    time.sleep(0.1)
                    
        except Exception as e:
            logger.error(f"摄像头 {camera_id} 线程出错: {str(e)}")
            
    def update_display(self):
        """更新显示线程"""
        while any(i in self.camera_streams for i in range(4)):
            try:
                for i in range(4):
                    if not self.frame_queues[i].empty():
                        frame = self.frame_queues[i].get()
                        
                        # 转换为PIL图像
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        pil_image = Image.fromarray(frame_rgb)
                        photo = ImageTk.PhotoImage(pil_image)
                        
                        # 更新标签
                        self.camera_labels[i].config(image=photo, text="")
                        self.camera_labels[i].image = photo  # 保持引用
                        
                time.sleep(0.033)  # 约30fps
                
            except Exception as e:
                logger.error(f"更新显示时出错: {str(e)}")
                time.sleep(0.1)
                
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """关闭应用程序时的清理工作"""
        self.stop_cameras()
        self.disconnect_from_remote()
        self.root.destroy()

if __name__ == "__main__":
    app = RemoteCameraViewer()
    app.run()
