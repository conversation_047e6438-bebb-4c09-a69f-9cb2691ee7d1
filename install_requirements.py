#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装远程摄像头查看器所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ 安装 {package} 失败")
        return False

def check_opencv():
    """检查OpenCV是否正确安装"""
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
        return True
    except ImportError:
        print("✗ OpenCV 未安装")
        return False

def main():
    print("开始安装远程摄像头查看器依赖包...")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        "opencv-python",
        "pillow", 
        "paramiko",
        "requests",
        "numpy"
    ]
    
    # 安装包
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    # 检查OpenCV
    print("\n检查OpenCV安装...")
    if check_opencv():
        print("✓ OpenCV 可以正常使用")
    else:
        print("✗ OpenCV 安装有问题，请手动安装")
        
    print("\n安装完成！现在可以运行摄像头查看器了。")
    print("使用方法:")
    print("  python simple_remote_camera.py  # 简化版（推荐）")
    print("  python remote_camera_viewer.py  # 完整版（需要SSH）")

if __name__ == "__main__":
    main()
