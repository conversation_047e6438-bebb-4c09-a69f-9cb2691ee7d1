#!/usr/bin/env python3
"""
多小车LED控制示例
演示如何使用重构后的代码控制多个小车的LED
"""

import rospy
from real_omni_led_ctrl import MultiRobotLEDController, MqttClientThread

def example_single_robot():
    """单个小车控制示例"""
    print("=== 单个小车控制示例 ===")
    
    # 初始化ROS节点
    rospy.init_node("single_robot_led_example")
    
    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "SingleRobotLED"
    mqtt_client = MqttClientThread(broker_ip, port, keepalive, client_id)
    mqtt_client.start_up_mqtt_thread()
    
    # 控制单个小车 (ID: 8)
    robot_ids = [8]
    multi_controller = MultiRobotLEDController(
        mqtt_client=mqtt_client,
        entity_ids=robot_ids
    )
    
    # 运行LED控制
    rate = rospy.Rate(12)
    count = 3 * (12 + 6)  # 3个循环周期
    
    while not rospy.is_shutdown() and count > 0:
        multi_controller.update_all_colors()
        multi_controller.publish_all()
        count -= 1
        print(f"单车控制 - count: {count}")
        rate.sleep()
    
    # 关闭LED
    multi_controller.set_all_off()
    print("单个小车控制完成")


def example_multiple_robots():
    """多个小车控制示例"""
    print("=== 多个小车控制示例 ===")
    
    # 初始化ROS节点
    rospy.init_node("multi_robot_led_example")
    
    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "MultiRobotLED"
    mqtt_client = MqttClientThread(broker_ip, port, keepalive, client_id)
    mqtt_client.start_up_mqtt_thread()
    
    # 控制多个小车 (ID: 8, 9, 10)
    robot_ids = [8, 9, 10]
    
    # 自定义LED配置 - 蓝色闪烁
    custom_config = {
        'colors': [0x0000FF, 0x000000],  # 蓝色闪烁
        'counts': [10, 5]
    }
    
    multi_controller = MultiRobotLEDController(
        mqtt_client=mqtt_client,
        entity_ids=robot_ids,
        led_config=custom_config
    )
    
    # 运行LED控制
    rate = rospy.Rate(12)
    count = 3 * (10 + 5)  # 3个循环周期
    
    print(f"开始控制小车: {robot_ids}")
    
    while not rospy.is_shutdown() and count > 0:
        multi_controller.update_all_colors()
        multi_controller.publish_all()
        count -= 1
        
        # 显示所有小车状态
        status_info = multi_controller.get_status_info()
        print(f"多车控制 - count: {count}, " + ", ".join(status_info))
        
        rate.sleep()
    
    # 关闭所有LED
    multi_controller.set_all_off()
    print("多个小车控制完成")


def example_custom_patterns():
    """自定义LED模式示例"""
    print("=== 自定义LED模式示例 ===")
    
    # 初始化ROS节点
    rospy.init_node("custom_pattern_led_example")
    
    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "CustomPatternLED"
    mqtt_client = MqttClientThread(broker_ip, port, keepalive, client_id)
    mqtt_client.start_up_mqtt_thread()
    
    # 不同小车使用不同的LED模式
    robot_configs = [
        {
            'robot_ids': [8],
            'config': {
                'colors': [0xFF0000, 0x000000],  # 红色闪烁
                'counts': [8, 4]
            },
            'name': '红色模式'
        },
        {
            'robot_ids': [9],
            'config': {
                'colors': [0x00FF00, 0x000000],  # 绿色闪烁
                'counts': [6, 6]
            },
            'name': '绿色模式'
        },
        {
            'robot_ids': [10],
            'config': {
                'colors': [0x0000FF, 0x000000],  # 蓝色闪烁
                'counts': [10, 2]
            },
            'name': '蓝色模式'
        }
    ]
    
    # 为每种模式创建控制器
    controllers = []
    for config in robot_configs:
        controller = MultiRobotLEDController(
            mqtt_client=mqtt_client,
            entity_ids=config['robot_ids'],
            led_config=config['config']
        )
        controllers.append((controller, config['name']))
        print(f"创建{config['name']}控制器，控制小车: {config['robot_ids']}")
    
    # 同时运行所有控制器
    rate = rospy.Rate(12)
    count = 60  # 5秒钟
    
    while not rospy.is_shutdown() and count > 0:
        for controller, name in controllers:
            controller.update_all_colors()
            controller.publish_all()
        
        count -= 1
        print(f"自定义模式 - count: {count}")
        rate.sleep()
    
    # 关闭所有LED
    for controller, name in controllers:
        controller.set_all_off()
        print(f"{name}已关闭")
    
    print("自定义模式控制完成")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == "single":
            example_single_robot()
        elif mode == "multi":
            example_multiple_robots()
        elif mode == "custom":
            example_custom_patterns()
        else:
            print("使用方法: python multi_robot_led_example.py [single|multi|custom]")
    else:
        print("请选择运行模式:")
        print("python multi_robot_led_example.py single   # 单个小车控制")
        print("python multi_robot_led_example.py multi    # 多个小车控制")
        print("python multi_robot_led_example.py custom   # 自定义模式控制")
