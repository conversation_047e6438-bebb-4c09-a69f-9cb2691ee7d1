#!/usr/bin/env python3
"""
同步多小车LED控制启动脚本
使用预设模式快速启动多小车同步LED控制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_multi_robot_led import run_sync_led_control
from led_patterns import get_pattern, list_patterns, LED_PATTERNS

def main():
    """主函数"""
    
    # ==================== 快速配置区域 ====================
    
    # 要控制的小车ID列表（修改这里来控制不同的小车）
    ROBOT_IDS = [8, 9, 10]
    
    # 运行参数
    CYCLES = 5      # 循环周期数
    FREQUENCY = 12  # 频率(Hz)
    
    # ==================== 命令行参数处理 ====================
    
    if len(sys.argv) < 2:
        print("同步多小车LED控制器")
        print("=" * 50)
        print("使用方法:")
        print(f"  python {sys.argv[0]} <模式名称> [小车ID列表] [循环次数] [频率]")
        print()
        print("基础示例:")
        print(f"  python {sys.argv[0]} red_flash")
        print(f"  python {sys.argv[0]} blue_flash 8,9,10")
        print(f"  python {sys.argv[0]} rgb_cycle 8,9,10 3 15")
        print(f"  python {sys.argv[0]} list  # 显示所有可用模式")
        print()
        print("摄像头优化模式 (低亮度):")
        print(f"  python {sys.argv[0]} red_camera    # 红色闪烁(低亮度)")
        print(f"  python {sys.argv[0]} blue_camera   # 蓝色闪烁(低亮度)")
        print(f"  python {sys.argv[0]} rgb_camera    # RGB循环(低亮度)")
        print(f"  python {sys.argv[0]} brightness_test  # 亮度对比测试")
        print()
        print("当前默认配置:")
        print(f"  小车ID: {ROBOT_IDS}")
        print(f"  循环次数: {CYCLES}")
        print(f"  频率: {FREQUENCY}Hz")
        return
    
    # 处理特殊命令
    if sys.argv[1] == "list":
        list_patterns()
        return
    
    # 获取模式名称
    pattern_name = sys.argv[1]
    
    # 处理小车ID列表参数
    if len(sys.argv) > 2:
        try:
            robot_ids_str = sys.argv[2]
            ROBOT_IDS = [int(x.strip()) for x in robot_ids_str.split(',')]
        except ValueError:
            print(f"错误: 无效的小车ID列表 '{sys.argv[2]}'")
            print("格式应为: 8,9,10")
            return
    
    # 处理循环次数参数
    if len(sys.argv) > 3:
        try:
            CYCLES = int(sys.argv[3])
        except ValueError:
            print(f"错误: 无效的循环次数 '{sys.argv[3]}'")
            return
    
    # 处理频率参数
    if len(sys.argv) > 4:
        try:
            FREQUENCY = int(sys.argv[4])
        except ValueError:
            print(f"错误: 无效的频率 '{sys.argv[4]}'")
            return
    
    # ==================== 获取LED模式 ====================
    
    pattern = get_pattern(pattern_name)
    if pattern is None:
        print(f"错误: 未找到模式 '{pattern_name}'")
        print("\n可用模式:")
        for name in LED_PATTERNS.keys():
            print(f"  {name}")
        print(f"\n使用 'python {sys.argv[0]} list' 查看详细信息")
        return
    
    # ==================== 显示配置信息 ====================
    
    pattern_info = LED_PATTERNS[pattern_name]
    print("同步多小车LED控制器")
    print("=" * 50)
    print(f"模式名称: {pattern_info['name']}")
    print(f"模式描述: {pattern_info['description']}")
    print(f"控制小车: {ROBOT_IDS}")
    print(f"颜色序列: {[hex(c) for c in pattern['colors']]}")
    print(f"计数序列: {pattern['counts']}")
    print(f"循环次数: {CYCLES}")
    print(f"频率: {FREQUENCY}Hz")
    print("=" * 50)
    
    # 确认启动
    try:
        input("按回车键开始控制，或按Ctrl+C取消...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    # ==================== 启动LED控制 ====================
    
    try:
        run_sync_led_control(
            robot_ids=ROBOT_IDS,
            colors=pattern['colors'],
            counts=pattern['counts'],
            cycles=CYCLES,
            frequency=FREQUENCY
        )
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭...")
    except Exception as e:
        print(f"错误: {e}")

def quick_start_examples():
    """快速启动示例"""
    print("快速启动示例:")
    print("=" * 50)
    
    examples = [
        ("红色闪烁", "python run_sync_led.py red_flash"),
        ("蓝色闪烁", "python run_sync_led.py blue_flash"),
        ("RGB循环", "python run_sync_led.py rgb_cycle"),
        ("警示模式", "python run_sync_led.py warning"),
        ("紧急模式", "python run_sync_led.py emergency"),
        ("就绪状态", "python run_sync_led.py ready"),
        ("控制特定小车", "python run_sync_led.py red_flash 8,9"),
        ("自定义参数", "python run_sync_led.py blue_flash 8,9,10 3 15"),
    ]
    
    for desc, cmd in examples:
        print(f"{desc:12} | {cmd}")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
