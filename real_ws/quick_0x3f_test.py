#!/usr/bin/env python3
"""
快速测试0x3f亮度值
直接使用实际代码中的0x3f亮度值进行LED控制测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_multi_robot_led import run_sync_led_control

def test_0x3f_colors(robot_ids):
    """
    测试0x3f亮度的各种颜色
    基于agent_management.py中的实际使用值
    """
    
    # 基于实际代码的颜色定义
    colors_0x3f = {
        "红色": 0x3f0000,
        "绿色": 0x003f00, 
        "蓝色": 0x00003f,
        "黄色": 0x3f3f00,
        "白色": 0x3f3f3f,
        "关闭": 0x000000
    }
    
    print("0x3f亮度LED颜色测试")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print("基于实际代码中使用的0x3f亮度值")
    print("=" * 40)
    
    for color_name, color_value in colors_0x3f.items():
        if color_value == 0x000000:
            continue  # 跳过黑色
            
        print(f"\n测试 {color_name} (0x{color_value:06X})")
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=[color_value, 0x000000],  # 颜色闪烁
                counts=[8, 4],  # 闪烁模式
                cycles=2,       # 2个周期
                frequency=12    # 12Hz
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
        
        print(f"{color_name}测试完成")

def test_trajectory_modes(robot_ids):
    """
    测试轨迹模式（基于实际代码中的模式0-4）
    """
    
    trajectory_modes = {
        "模式0 (关闭)": [0x000000],
        "模式1 (白色)": [0x3f3f3f, 0x000000],
        "模式2 (绿色)": [0x003f00, 0x000000], 
        "模式3 (红色)": [0x3f0000, 0x000000],
        "模式4 (黄色)": [0x3f3f00, 0x000000]
    }
    
    print("轨迹模式测试 (基于实际代码)")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print("=" * 40)
    
    for mode_name, colors in trajectory_modes.items():
        print(f"\n测试 {mode_name}")
        print(f"颜色: {[hex(c) for c in colors]}")
        
        if len(colors) == 1:  # 常亮模式
            counts = [24]  # 2秒常亮
        else:  # 闪烁模式
            counts = [12, 6]  # 闪烁模式
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=colors,
                counts=counts,
                cycles=1,
                frequency=12
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
        
        print(f"{mode_name}测试完成")

def quick_red_flash(robot_ids):
    """
    快速红色闪烁测试 (0x3f0000)
    """
    print("快速红色闪烁测试 (0x3f0000)")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print("红色亮度: 0x3f (63/255 ≈ 25%)")
    print("=" * 40)
    
    try:
        run_sync_led_control(
            robot_ids=robot_ids,
            colors=[0x3f0000, 0x000000],  # 红色闪烁
            counts=[12, 6],               # 红12次，黑6次
            cycles=5,                     # 5个周期
            frequency=12                  # 12Hz
        )
    except KeyboardInterrupt:
        print("\n用户中断测试")
    
    print("红色闪烁测试完成")

def main():
    """主函数"""
    
    # 默认小车ID
    default_robot_ids = [8, 9, 10]
    
    if len(sys.argv) < 2:
        print("0x3f亮度LED测试工具")
        print("=" * 50)
        print("使用方法:")
        print(f"  python {sys.argv[0]} red [小车ID列表]")
        print(f"  python {sys.argv[0]} colors [小车ID列表]")
        print(f"  python {sys.argv[0]} trajectory [小车ID列表]")
        print()
        print("示例:")
        print(f"  python {sys.argv[0]} red")
        print(f"  python {sys.argv[0]} red 8,9")
        print(f"  python {sys.argv[0]} colors 8,9,10")
        print(f"  python {sys.argv[0]} trajectory")
        print()
        print("说明:")
        print("  red        - 快速红色闪烁测试")
        print("  colors     - 测试所有0x3f亮度颜色")
        print("  trajectory - 测试轨迹模式0-4")
        print()
        print(f"默认小车ID: {default_robot_ids}")
        return
    
    test_type = sys.argv[1]
    robot_ids = default_robot_ids
    
    # 处理小车ID参数
    if len(sys.argv) > 2:
        try:
            robot_ids = [int(x.strip()) for x in sys.argv[2].split(',')]
        except ValueError:
            print(f"错误: 无效的小车ID列表 '{sys.argv[2]}'")
            print("格式应为: 8,9,10")
            return
    
    print(f"使用小车ID: {robot_ids}")
    print(f"测试类型: {test_type}")
    
    try:
        input("按回车键开始测试，或按Ctrl+C取消...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    # 执行对应的测试
    if test_type == "red":
        quick_red_flash(robot_ids)
    elif test_type == "colors":
        test_0x3f_colors(robot_ids)
    elif test_type == "trajectory":
        test_trajectory_modes(robot_ids)
    else:
        print(f"错误: 未知的测试类型 '{test_type}'")
        print("支持的类型: red, colors, trajectory")

if __name__ == "__main__":
    main()
