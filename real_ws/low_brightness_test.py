#!/usr/bin/env python3
"""
低亮度LED测试脚本
测试不同的低亮度级别，找到最适合摄像头的亮度
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_multi_robot_led import run_sync_led_control

def test_low_brightness_levels(robot_ids, color="red"):
    """
    测试低亮度级别
    从0x03到0x20，找到最适合摄像头的亮度
    """
    
    # 定义低亮度级别
    brightness_levels = {
        "red": [
            ("极低亮度 (0x03)", [0x030000, 0x000000]),
            ("很低亮度 (0x05)", [0x050000, 0x000000]),
            ("低亮度 (0x08)", [0x080000, 0x000000]),
            ("较低亮度 (0x0C)", [0x0C0000, 0x000000]),
            ("中低亮度 (0x10)", [0x100000, 0x000000]),
            ("稍低亮度 (0x18)", [0x180000, 0x000000]),
            ("中等偏低 (0x20)", [0x200000, 0x000000]),
        ],
        "green": [
            ("极低亮度 (0x03)", [0x000300, 0x000000]),
            ("很低亮度 (0x05)", [0x000500, 0x000000]),
            ("低亮度 (0x08)", [0x000800, 0x000000]),
            ("较低亮度 (0x0C)", [0x000C00, 0x000000]),
            ("中低亮度 (0x10)", [0x001000, 0x000000]),
            ("稍低亮度 (0x18)", [0x001800, 0x000000]),
            ("中等偏低 (0x20)", [0x002000, 0x000000]),
        ],
        "blue": [
            ("极低亮度 (0x03)", [0x000003, 0x000000]),
            ("很低亮度 (0x05)", [0x000005, 0x000000]),
            ("低亮度 (0x08)", [0x000008, 0x000000]),
            ("较低亮度 (0x0C)", [0x00000C, 0x000000]),
            ("中低亮度 (0x10)", [0x000010, 0x000000]),
            ("稍低亮度 (0x18)", [0x000018, 0x000000]),
            ("中等偏低 (0x20)", [0x000020, 0x000000]),
        ]
    }
    
    if color not in brightness_levels:
        print(f"错误: 不支持的颜色 '{color}'")
        print("支持的颜色: red, green, blue")
        return
    
    levels = brightness_levels[color]
    
    print(f"低亮度LED测试 - {color.upper()}色")
    print("=" * 60)
    print(f"控制小车: {robot_ids}")
    print(f"测试颜色: {color}")
    print("每个亮度级别将持续3秒钟")
    print("观察哪个亮度最适合摄像头成像")
    print("=" * 60)
    
    try:
        input("按回车键开始测试，或按Ctrl+C取消...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    for i, (level_name, colors) in enumerate(levels):
        print(f"\n[{i+1}/{len(levels)}] 测试 {level_name}")
        print(f"颜色值: {hex(colors[0])}")
        print("持续3秒...")
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=colors,
                counts=[9, 9],  # 快速闪烁便于观察
                cycles=1,       # 1个周期，约3秒
                frequency=6     # 6Hz，便于观察
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
        
        if i < len(levels) - 1:
            print("等待1秒后进行下一个测试...")
            import time
            time.sleep(1)
    
    print(f"\n{color}色低亮度测试完成！")
    print("请选择最适合摄像头的亮度级别。")

def quick_comparison(robot_ids):
    """
    快速对比几个关键亮度级别
    """
    print("快速亮度对比测试")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print("对比: 0x03 -> 0x08 -> 0x10 -> 0x20")
    print("每个亮度持续2秒")
    print("=" * 40)
    
    try:
        input("按回车键开始对比测试...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    # 快速对比几个关键亮度
    brightness_values = [
        ("极低 (0x03)", 0x030000),
        ("低 (0x08)", 0x080000),
        ("中低 (0x10)", 0x100000),
        ("稍高 (0x20)", 0x200000)
    ]
    
    for name, color_value in brightness_values:
        print(f"\n测试 {name} - 0x{color_value:06X}")
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=[color_value, 0x000000],
                counts=[6, 6],  # 2秒闪烁
                cycles=1,
                frequency=6
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
    
    print("\n快速对比测试完成！")

def test_recommended_brightness(robot_ids, brightness=0x08):
    """
    测试推荐的亮度值
    """
    print(f"推荐亮度测试 - 0x{brightness:02X}")
    print("=" * 40)
    print(f"控制小车: {robot_ids}")
    print(f"亮度值: 0x{brightness:02X} ({brightness}/255 ≈ {brightness/255*100:.1f}%)")
    print("=" * 40)
    
    # 测试红绿蓝三色
    colors = [
        ("红色", (brightness << 16)),
        ("绿色", (brightness << 8)),
        ("蓝色", brightness),
        ("黄色", (brightness << 16) | (brightness << 8)),
        ("白色", (brightness << 16) | (brightness << 8) | brightness)
    ]
    
    try:
        input("按回车键开始测试...")
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    for color_name, color_value in colors:
        print(f"\n测试 {color_name} (0x{color_value:06X})")
        
        try:
            run_sync_led_control(
                robot_ids=robot_ids,
                colors=[color_value, 0x000000],
                counts=[8, 4],  # 闪烁模式
                cycles=1,
                frequency=8
            )
        except KeyboardInterrupt:
            print("\n用户中断测试")
            break
    
    print(f"\n0x{brightness:02X}亮度测试完成！")

def main():
    """主函数"""
    
    # 默认配置
    default_robot_ids = [8, 9, 10]
    
    if len(sys.argv) < 2:
        print("低亮度LED测试工具")
        print("=" * 50)
        print("使用方法:")
        print(f"  python {sys.argv[0]} levels <颜色> [小车ID列表]")
        print(f"  python {sys.argv[0]} quick [小车ID列表]")
        print(f"  python {sys.argv[0]} test <亮度值> [小车ID列表]")
        print()
        print("示例:")
        print(f"  python {sys.argv[0]} levels red")
        print(f"  python {sys.argv[0]} levels blue 8,9")
        print(f"  python {sys.argv[0]} quick")
        print(f"  python {sys.argv[0]} test 8")
        print(f"  python {sys.argv[0]} test 16 8,9,10")
        print()
        print("说明:")
        print("  levels  - 测试一系列低亮度级别")
        print("  quick   - 快速对比几个关键亮度")
        print("  test    - 测试指定亮度值 (十进制，如8表示0x08)")
        print()
        print(f"默认小车ID: {default_robot_ids}")
        print("推荐亮度: 0x08 (8) 或 0x10 (16)")
        return
    
    test_type = sys.argv[1]
    
    if test_type == "levels":
        if len(sys.argv) < 3:
            print("错误: 请指定颜色 (red/green/blue)")
            return
        
        color = sys.argv[2]
        robot_ids = default_robot_ids
        
        if len(sys.argv) > 3:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[3].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[3]}'")
                return
        
        test_low_brightness_levels(robot_ids, color)
    
    elif test_type == "quick":
        robot_ids = default_robot_ids
        if len(sys.argv) > 2:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[2].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[2]}'")
                return
        
        quick_comparison(robot_ids)
    
    elif test_type == "test":
        if len(sys.argv) < 3:
            print("错误: 请指定亮度值 (十进制)")
            return
        
        try:
            brightness = int(sys.argv[2])
            if not 1 <= brightness <= 255:
                print("错误: 亮度值必须在1-255之间")
                return
        except ValueError:
            print("错误: 亮度值必须是数字")
            return
        
        robot_ids = default_robot_ids
        if len(sys.argv) > 3:
            try:
                robot_ids = [int(x.strip()) for x in sys.argv[3].split(',')]
            except ValueError:
                print(f"错误: 无效的小车ID列表 '{sys.argv[3]}'")
                return
        
        test_recommended_brightness(robot_ids, brightness)
    
    else:
        print(f"错误: 未知的测试类型 '{test_type}'")
        print("支持的类型: levels, quick, test")

if __name__ == "__main__":
    main()
