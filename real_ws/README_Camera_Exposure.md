# 摄像头曝光控制指南

## 📋 概述

这个指南帮助你调整ROS摄像头的曝光、亮度、对比度等参数，以获得更好的LED检测效果。

## 🚀 快速开始

### 1. 使用曝光控制工具

```bash
# 运行曝光控制工具
python camera_exposure_control.py
```

### 2. 手动调整摄像头参数

```bash
# 查看可用摄像头设备
ls /dev/video* /dev/cam*

# 查看摄像头控制参数
v4l2-ctl --device /dev/cam0 --list-ctrls

# 获取当前设置
v4l2-ctl --device /dev/cam0 --get-ctrl brightness,contrast,exposure_auto,exposure_absolute
```

## 🎛️ 主要参数说明

### 曝光控制
- **自动曝光**: `exposure_auto=3` (推荐用于一般场景)
- **手动曝光**: `exposure_auto=1` (推荐用于LED检测)
- **曝光值**: `exposure_absolute=50-200` (值越小越暗，适合LED检测)

### 图像质量
- **亮度**: `brightness=0-255` (推荐100-150)
- **对比度**: `contrast=0-255` (推荐120-180，高对比度有助于LED检测)
- **饱和度**: `saturation=0-255` (推荐128)

## 🎯 LED检测优化设置

### 推荐参数
```bash
# 设置手动曝光，降低曝光值
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_auto=1
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_absolute=50

# 调整亮度和对比度
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=100
v4l2-ctl --device /dev/cam0 --set-ctrl contrast=150
```

### 为什么这样设置？
1. **低曝光**: 防止LED过曝，保持颜色清晰
2. **中等亮度**: 保证背景可见但不过亮
3. **高对比度**: 增强LED与背景的区别

## 📝 常用命令

### 查看设备信息
```bash
# 列出所有摄像头设备
v4l2-ctl --list-devices

# 查看设备支持的格式
v4l2-ctl --device /dev/cam0 --list-formats-ext

# 查看所有控制参数
v4l2-ctl --device /dev/cam0 --list-ctrls-menus
```

### 设置参数
```bash
# 设置自动曝光
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_auto=3

# 设置手动曝光
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_auto=1
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_absolute=100

# 设置亮度
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=128

# 设置对比度
v4l2-ctl --device /dev/cam0 --set-ctrl contrast=128

# 批量设置
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=100,contrast=150,exposure_auto=1,exposure_absolute=50
```

### 保存和恢复设置
```bash
# 保存当前设置到文件
v4l2-ctl --device /dev/cam0 --list-ctrls > camera_settings.txt

# 查看特定参数
v4l2-ctl --device /dev/cam0 --get-ctrl brightness,contrast,exposure_auto,exposure_absolute
```

## 🔧 在ROS Launch文件中设置

### 方法1: 在launch文件中设置参数
```xml
<launch>
  <node name="cam0" pkg="usb_cam" type="usb_cam_node" output="screen">
    <param name="video_device" value="/dev/cam0" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam0" />
    
    <!-- 曝光和图像质量设置 -->
    <param name="brightness" value="100" />
    <param name="contrast" value="150" />
    <param name="auto_exposure" value="1" />
    <param name="exposure" value="50" />
  </node>
</launch>
```

### 方法2: 启动前设置v4l2参数
```bash
# 在启动ROS节点前设置参数
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_auto=1,exposure_absolute=50,brightness=100,contrast=150

# 然后启动摄像头节点
roslaunch usb_cam usb_cam-test.launch
```

## 🎨 针对不同LED亮度的设置

### 高亮度LED (如原始0xFF)
```bash
# 非常低的曝光值
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_absolute=20
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=80
```

### 中等亮度LED (如0x3f)
```bash
# 适中的曝光值
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_absolute=50
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=100
```

### 低亮度LED (如0x10)
```bash
# 较高的曝光值
v4l2-ctl --device /dev/cam0 --set-ctrl exposure_absolute=100
v4l2-ctl --device /dev/cam0 --set-ctrl brightness=120
```

## 🔍 故障排除

### 常见问题

1. **命令不存在**: 
   ```bash
   sudo apt-get install v4l-utils
   ```

2. **权限不足**:
   ```bash
   sudo usermod -a -G video $USER
   # 然后重新登录
   ```

3. **设备不存在**:
   ```bash
   # 检查设备是否正确连接
   lsusb | grep -i camera
   dmesg | grep video
   ```

4. **参数不支持**:
   ```bash
   # 查看设备支持的参数
   v4l2-ctl --device /dev/cam0 --list-ctrls
   ```

### 测试设置效果

```bash
# 启动图像查看器测试效果
rosrun image_view image_view image:=/cam0/image_raw

# 或者使用rqt
rqt_image_view
```

## 📊 参数对比表

| 场景 | 曝光模式 | 曝光值 | 亮度 | 对比度 | 说明 |
|------|----------|--------|------|--------|------|
| 一般使用 | 自动(3) | - | 128 | 128 | 默认设置 |
| 高亮LED | 手动(1) | 20-30 | 80-100 | 150-180 | 防止过曝 |
| 中亮LED | 手动(1) | 40-60 | 100-120 | 140-160 | 平衡设置 |
| 低亮LED | 手动(1) | 80-120 | 120-140 | 130-150 | 增强亮度 |

## 🚀 自动化脚本

创建一个快速设置脚本：

```bash
#!/bin/bash
# quick_camera_setup.sh

DEVICE=${1:-/dev/cam0}
MODE=${2:-led}

case $MODE in
  "led")
    echo "设置LED检测优化参数..."
    v4l2-ctl --device $DEVICE --set-ctrl exposure_auto=1,exposure_absolute=50,brightness=100,contrast=150
    ;;
  "normal")
    echo "设置正常参数..."
    v4l2-ctl --device $DEVICE --set-ctrl exposure_auto=3,brightness=128,contrast=128
    ;;
  *)
    echo "用法: $0 [设备] [模式]"
    echo "设备: /dev/cam0, /dev/cam1, etc."
    echo "模式: led, normal"
    ;;
esac
```

使用方法：
```bash
chmod +x quick_camera_setup.sh
./quick_camera_setup.sh /dev/cam0 led
```

## 💡 提示

1. **实时调整**: 可以在ROS节点运行时动态调整v4l2参数
2. **多摄像头**: 每个摄像头设备需要单独设置
3. **重启保持**: 某些设置在设备重启后会重置，需要在启动脚本中重新设置
4. **测试效果**: 建议配合LED闪烁测试，观察最佳参数组合
