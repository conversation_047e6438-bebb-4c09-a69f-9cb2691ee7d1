# 同步多小车LED控制系统

## 📋 概述

这是一个专门为多个小车使用相同灯光模式而设计的LED控制系统。所有小车将完全同步地执行相同的LED闪烁模式。

## 🚀 快速开始

### 最简单的使用方法

```bash
# 使用红色闪烁模式控制默认小车 (8, 9, 10)
python run_sync_led.py red_flash

# 使用蓝色闪烁模式
python run_sync_led.py blue_flash

# 使用RGB循环模式
python run_sync_led.py rgb_cycle
```

### 指定小车ID

```bash
# 只控制小车8和9
python run_sync_led.py red_flash 8,9

# 控制小车8, 9, 10, 11
python run_sync_led.py blue_flash 8,9,10,11
```

### 自定义参数

```bash
# 模式 小车ID 循环次数 频率
python run_sync_led.py red_flash 8,9,10 3 15
```

## 📁 文件说明

### 核心文件

1. **`sync_multi_robot_led.py`** - 主要的同步LED控制器
2. **`led_patterns.py`** - LED模式配置文件
3. **`run_sync_led.py`** - 简单的启动脚本
4. **`real_omni_led_ctrl.py`** - 原始代码的改进版本

### 使用哪个文件？

- **简单使用**: 使用 `run_sync_led.py`
- **高级定制**: 直接使用 `sync_multi_robot_led.py`
- **模式管理**: 编辑 `led_patterns.py`

## 🎨 可用的LED模式

### 查看所有模式

```bash
python run_sync_led.py list
```

### 常用模式

| 模式名称 | 描述 | 颜色 |
|---------|------|------|
| `red_flash` | 红色闪烁 | 红色闪烁12次，熄灭6次 |
| `blue_flash` | 蓝色闪烁 | 蓝色闪烁10次，熄灭5次 |
| `green_flash` | 绿色闪烁 | 绿色闪烁8次，熄灭8次 |
| `rgb_cycle` | RGB循环 | 红绿蓝循环显示 |
| `warning` | 警示模式 | 红黄交替警示 |
| `emergency` | 紧急模式 | 红色紧急闪烁 |
| `ready` | 就绪状态 | 绿色表示就绪 |
| `working` | 工作状态 | 蓝色表示工作 |

## ⚙️ 配置说明

### 默认配置

在 `run_sync_led.py` 中可以修改默认配置：

```python
# 要控制的小车ID列表
ROBOT_IDS = [8, 9, 10]

# 运行参数
CYCLES = 5      # 循环周期数
FREQUENCY = 12  # 频率(Hz)
```

### 自定义LED模式

在 `led_patterns.py` 中添加新模式：

```python
LED_PATTERNS["my_pattern"] = {
    "name": "我的模式",
    "colors": [0xFF0000, 0x00FF00, 0x000000],  # 红绿黑
    "counts": [5, 5, 3],                       # 各显示次数
    "description": "红绿交替闪烁"
}
```

## 🔧 高级使用

### 直接使用核心控制器

```python
from sync_multi_robot_led import run_sync_led_control

# 自定义控制
run_sync_led_control(
    robot_ids=[8, 9, 10],
    colors=[0xFF0000, 0x000000],  # 红色闪烁
    counts=[12, 6],
    cycles=5,
    frequency=12
)
```

### 在代码中使用

```python
from sync_multi_robot_led import SyncMultiRobotLEDController, MqttClientThread

# 创建MQTT客户端
mqtt_client = MqttClientThread("*********", 1883, 60, "MyClient")
mqtt_client.start_up_mqtt_thread()

# 创建LED控制器
controller = SyncMultiRobotLEDController(
    mqtt_client=mqtt_client,
    robot_ids=[8, 9, 10],
    colors=[0xFF0000, 0x000000],
    counts=[12, 6]
)

# 控制循环
for i in range(100):
    controller.update_color()
    controller.publish_all()
    time.sleep(1/12)  # 12Hz

# 关闭LED
controller.turn_off_all()
```

## 🎯 使用场景

### 1. 编队飞行/行驶
所有小车使用相同的LED模式，便于识别编队状态。

```bash
python run_sync_led.py ready 1,2,3,4,5  # 编队就绪
python run_sync_led.py working 1,2,3,4,5  # 编队工作中
```

### 2. 状态指示
根据任务状态切换不同的LED模式。

```bash
python run_sync_led.py ready     # 就绪状态
python run_sync_led.py working   # 工作状态  
python run_sync_led.py warning   # 警告状态
python run_sync_led.py emergency # 紧急状态
```

### 3. 演示表演
使用彩色循环模式进行演示。

```bash
python run_sync_led.py rainbow_cycle  # 彩虹循环
python run_sync_led.py rgb_cycle      # RGB循环
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查MQTT服务器地址 (默认: *********)
   - 确保网络连接正常

2. **小车不响应**
   - 确认小车ID是否正确
   - 检查小车是否在线

3. **LED不同步**
   - 确保所有小车使用相同的代码版本
   - 检查网络延迟

### 调试模式

在代码中添加更多调试信息：

```python
# 在 sync_multi_robot_led.py 中取消注释调试行
print(f"发送到 VSWARM{entity_id}: {hex(led_colors)}")
```

## 📝 更新日志

### v2.0 (当前版本)
- ✅ 解决了entity_id重复赋值问题
- ✅ 支持多小车同步控制
- ✅ 添加了丰富的预设LED模式
- ✅ 提供了简单易用的启动脚本
- ✅ 完善的文档和示例

### v1.0 (原始版本)
- 基础的单小车LED控制
- entity_id需要设置两次
- 硬编码的配置

## 🤝 贡献

如果你有新的LED模式想法或改进建议，欢迎：

1. 在 `led_patterns.py` 中添加新模式
2. 提交改进建议
3. 报告bug或问题

## 📞 支持

如果遇到问题，请检查：
1. 网络连接
2. 小车状态
3. 配置参数
4. 错误日志
