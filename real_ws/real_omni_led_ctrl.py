import json
import os
import time
import rospy
from std_msgs.msg import Float32MultiArray
from paho.mqtt import client as mqtt_client

# 设置LED灯的颜色
def LedSetColor(color_list, color):
    color_list.clear()
    color_list.append(((color >> 16) & 0xff) / 256)  # 红色分量
    color_list.append(((color >> 8) & 0xff) / 256)   # 绿色分量
    color_list.append(((color) & 0xff) / 256)        # 蓝色分量
    color_list.append(1)                             # 透明度

# LED控制器类
class LEDController:
    def __init__(self, colors, counts, color_list, mqtt_client, entity_id):
        self.colors = colors                  # 颜色列表
        self.counts = counts                  # 每种颜色的显示次数
        self.current_color_idx = 0            # 当前颜色的索引
        self.current_count = 0                # 当前颜色已显示的次数
        self.color_list = color_list          # 存放当前颜色的列表
        self.mqtt_client = mqtt_client        # MQTT客户端
        self.entity_id = entity_id            # 实体ID

    def update_color(self):
        # 更新当前颜色的显示次数
        self.current_count += 1

        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

        # 设置当前颜色
        LedSetColor(self.color_list, self.colors[self.current_color_idx])

    def publish(self):
        # 发布当前颜色到MQTT
        self.set_ledup(self.entity_id, self.colors[self.current_color_idx])
        self.set_leddown(self.entity_id, self.colors[self.current_color_idx])
        # print(f"LED color updated to {self.colors[self.current_color_idx]}")

    def set_ledup(self, entity_id, led_colors):
        json_msg = {
            "cmd_type": "ledup",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 14,
                "2": led_colors,
                "3": 14,
                "4": led_colors,
                "5": 14,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )

    def set_leddown(self, entity_id, led_colors):
        json_msg = {
            "cmd_type": "leddown",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 30,
                "2": led_colors,
                "3": 30,
                "4": led_colors,
                "5": 30,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )


class MqttClientThread:
    def __init__(self, broker, port, keepalive, client_id):
        self.broker = broker  # MQTT代理服务器地址
        self.port = port
        self.keepalive = keepalive
        self.reconnect_interval = 1
        self.client_id = client_id
        self.client = self.connect_mqtt()


    def connect_mqtt(self):
        """连接MQTT代理服务器"""

        def on_connect(client, userdata, flags, rc):
            """连接回调函数"""
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print(f"Failed to connect, return code {rc}")
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client

    def start_up_mqtt_thread(self):
        """初始化并启动MQTT线程"""

        try:
            broker = os.environ.get("REMOTE_SERVER", self.broker)
            net_status = -1
            while net_status != 0:
                net_status = os.system(f"ping -c 4 {broker}")
                time.sleep(2)
            self.client.loop_start()
        except Exception as e:
            print(f"Error starting MQTT thread: {e}")

    def run(self):
        """启动MQTT客户端"""
        while True:
            try:
                self.client.loop_forever()
            except Exception as e:
                print(f"Error in MQTT loop: {e}")
                time.sleep(self.reconnect_interval)

    def publish(self, topic, msg):
        """发布消息到指定主题"""
        result = self.client.publish(topic, msg)
        status = result[0]
        if status == 0:
            pass
        else:
            print(f"Failed to send message to topic {topic}")

# 多小车LED控制器管理类
class MultiRobotLEDController:
    def __init__(self, mqtt_client, entity_ids, led_config=None):
        """
        初始化多小车LED控制器

        Args:
            mqtt_client: MQTT客户端实例
            entity_ids: 小车ID列表，例如 [8, 9, 10]
            led_config: LED配置字典，包含colors和counts，如果为None则使用默认配置
        """
        self.mqtt_client = mqtt_client
        self.entity_ids = entity_ids
        self.controllers = {}

        # 默认LED配置
        default_config = {
            'colors': [0xFF0000, 0x000000],  # 红色闪烁
            'counts': [12, 6]
        }

        self.led_config = led_config if led_config else default_config

        # 为每个小车创建LED控制器
        for entity_id in self.entity_ids:
            color_list = []
            self.controllers[entity_id] = LEDController(
                colors=self.led_config['colors'],
                counts=self.led_config['counts'],
                color_list=color_list,
                mqtt_client=self.mqtt_client,
                entity_id=entity_id
            )

    def update_all_colors(self):
        """更新所有小车的LED颜色"""
        for controller in self.controllers.values():
            controller.update_color()

    def publish_all(self):
        """发布所有小车的LED状态"""
        for controller in self.controllers.values():
            controller.publish()

    def set_all_off(self):
        """关闭所有小车的LED"""
        for entity_id in self.controllers.keys():
            # 创建关闭LED的控制器
            color_list = []
            off_controller = LEDController(
                colors=[0x000000, 0x000000],
                counts=[6, 6],
                color_list=color_list,
                mqtt_client=self.mqtt_client,
                entity_id=entity_id
            )
            off_controller.update_color()
            off_controller.publish()

    def get_status_info(self):
        """获取所有小车的状态信息"""
        status_info = []
        for entity_id, controller in self.controllers.items():
            current_color = controller.colors[controller.current_color_idx]
            status_info.append(f"VSWARM{entity_id}: {hex(current_color)}")
        return status_info


if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("user_led_ctrl")

    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "LEDController"
    mqtt_client_instance = MqttClientThread(broker_ip, port, keepalive, client_id)

    # 启动MQTT线程
    mqtt_client_instance.start_up_mqtt_thread()

    # 配置要控制的小车ID列表 - 在这里一次性设置所有要控制的小车
    robot_ids = [8, 9, 10]  # 可以根据需要修改这个列表

    # 可选：自定义LED配置
    custom_led_config = {
        'colors': [0xFF0000, 0x000000],  # 红色闪烁
        'counts': [12, 6]
    }

    # 创建多小车LED控制器
    multi_controller = MultiRobotLEDController(
        mqtt_client=mqtt_client_instance,
        entity_ids=robot_ids,
        led_config=custom_led_config
    )

    # 设置循环频率为12Hz
    rate = rospy.Rate(12)
    # 设置循环次数
    count = 5 * (12 + 6) - 1    # 5次循环周期

    print(f"开始控制小车: {robot_ids}")

    # 进入循环，直到ROS节点关闭
    while not rospy.is_shutdown() and count > 0:
        # 更新并发布所有小车的LED颜色
        multi_controller.update_all_colors()
        multi_controller.publish_all()
        count = count - 1

        # 显示状态信息
        status_info = multi_controller.get_status_info()
        print(f"count: {count}, " + ", ".join(status_info))

        # 等待下一次循环
        rate.sleep()

    # 灯语结束后，关闭所有小车的LED
    print("关闭所有小车的LED...")
    multi_controller.set_all_off()
