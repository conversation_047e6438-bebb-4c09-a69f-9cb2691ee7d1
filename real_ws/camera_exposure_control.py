#!/usr/bin/env python3
"""
摄像头曝光控制工具
用于调整ROS摄像头的曝光、亮度、对比度等参数
"""

import os
import subprocess
import sys
import time

class CameraExposureController:
    """摄像头曝光控制器"""
    
    def __init__(self):
        self.available_devices = []
        self.scan_camera_devices()
    
    def scan_camera_devices(self):
        """扫描可用的摄像头设备"""
        print("扫描摄像头设备...")
        
        # 检查/dev/video*设备
        try:
            result = subprocess.run(['ls', '/dev/video*'], 
                                  capture_output=True, text=True, check=True)
            video_devices = result.stdout.strip().split('\n')
            
            for device in video_devices:
                if device:
                    self.available_devices.append(device)
                    print(f"发现设备: {device}")
        except subprocess.CalledProcessError:
            print("未找到/dev/video*设备")
        
        # 检查/dev/cam*设备（符号链接）
        try:
            result = subprocess.run(['ls', '/dev/cam*'], 
                                  capture_output=True, text=True, check=True)
            cam_devices = result.stdout.strip().split('\n')
            
            for device in cam_devices:
                if device:
                    self.available_devices.append(device)
                    print(f"发现设备: {device}")
        except subprocess.CalledProcessError:
            print("未找到/dev/cam*设备")
        
        if not self.available_devices:
            print("警告: 未找到任何摄像头设备")
    
    def list_camera_controls(self, device):
        """列出摄像头的可用控制参数"""
        print(f"\n查询设备 {device} 的控制参数...")
        
        try:
            # 使用v4l2-ctl查询控制参数
            result = subprocess.run(['v4l2-ctl', '--device', device, '--list-ctrls'], 
                                  capture_output=True, text=True, check=True)
            print("可用的控制参数:")
            print(result.stdout)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"查询失败: {e}")
            return None
        except FileNotFoundError:
            print("错误: 未找到v4l2-ctl命令，请安装v4l-utils包")
            print("安装命令: sudo apt-get install v4l-utils")
            return None
    
    def get_current_settings(self, device):
        """获取当前摄像头设置"""
        print(f"\n获取设备 {device} 的当前设置...")
        
        try:
            result = subprocess.run(['v4l2-ctl', '--device', device, '--get-ctrl', 
                                   'brightness,contrast,saturation,exposure_auto,exposure_absolute'], 
                                  capture_output=True, text=True, check=True)
            print("当前设置:")
            print(result.stdout)
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"获取设置失败: {e}")
            return None
    
    def set_exposure_manual(self, device, exposure_value):
        """设置手动曝光"""
        print(f"\n设置设备 {device} 为手动曝光模式...")
        
        try:
            # 首先设置为手动曝光模式
            subprocess.run(['v4l2-ctl', '--device', device, '--set-ctrl', 'exposure_auto=1'], 
                          check=True)
            print("已设置为手动曝光模式")
            
            # 设置曝光值
            subprocess.run(['v4l2-ctl', '--device', device, '--set-ctrl', 
                           f'exposure_absolute={exposure_value}'], check=True)
            print(f"已设置曝光值为: {exposure_value}")
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"设置曝光失败: {e}")
            return False
    
    def set_exposure_auto(self, device):
        """设置自动曝光"""
        print(f"\n设置设备 {device} 为自动曝光模式...")
        
        try:
            subprocess.run(['v4l2-ctl', '--device', device, '--set-ctrl', 'exposure_auto=3'], 
                          check=True)
            print("已设置为自动曝光模式")
            return True
        except subprocess.CalledProcessError as e:
            print(f"设置自动曝光失败: {e}")
            return False
    
    def set_brightness(self, device, brightness):
        """设置亮度"""
        print(f"\n设置设备 {device} 亮度为: {brightness}")
        
        try:
            subprocess.run(['v4l2-ctl', '--device', device, '--set-ctrl', 
                           f'brightness={brightness}'], check=True)
            print(f"已设置亮度为: {brightness}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"设置亮度失败: {e}")
            return False
    
    def set_contrast(self, device, contrast):
        """设置对比度"""
        print(f"\n设置设备 {device} 对比度为: {contrast}")
        
        try:
            subprocess.run(['v4l2-ctl', '--device', device, '--set-ctrl', 
                           f'contrast={contrast}'], check=True)
            print(f"已设置对比度为: {contrast}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"设置对比度失败: {e}")
            return False
    
    def create_custom_launch_file(self, device, exposure_mode="auto", exposure_value=100, 
                                brightness=128, contrast=128):
        """创建自定义的摄像头launch文件"""
        
        device_name = device.split('/')[-1]  # 从/dev/cam0获取cam0
        launch_content = f"""<launch>
  <!-- 自定义摄像头配置 - {device_name} -->
  <node name="{device_name}" pkg="usb_cam" type="usb_cam_node" output="screen">
    <!-- 基本设置 -->
    <param name="video_device" value="{device}" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_{device_name}" />
    <param name="io_method" value="mmap"/>
    <param name="framerate" value="30"/>
    
    <!-- 曝光和图像质量设置 -->
    <param name="brightness" value="{brightness}" />
    <param name="contrast" value="{contrast}" />
    <param name="saturation" value="128" />
    <param name="sharpness" value="128" />
    
    <!-- 曝光设置 -->
    <param name="auto_exposure" value="{'3' if exposure_mode == 'auto' else '1'}" />
    <param name="exposure" value="{exposure_value}" />
    
    <!-- 自动白平衡 -->
    <param name="auto_white_balance" value="true" />
    
    <!-- 发布话题 -->
    <remap from="image_raw" to="/{device_name}/image_raw"/>
    <remap from="camera_info" to="/{device_name}/camera_info"/>
  </node>
</launch>"""
        
        filename = f"camera_{device_name}_custom.launch"
        filepath = os.path.join(os.getcwd(), filename)
        
        with open(filepath, 'w') as f:
            f.write(launch_content)
        
        print(f"\n已创建自定义launch文件: {filepath}")
        print(f"使用命令启动: roslaunch {filename}")
        
        return filepath

def main():
    """主函数"""
    controller = CameraExposureController()
    
    if not controller.available_devices:
        print("未找到摄像头设备，请检查硬件连接")
        return
    
    print("\n摄像头曝光控制工具")
    print("=" * 50)
    
    while True:
        print("\n可用选项:")
        print("1. 列出所有摄像头设备")
        print("2. 查看摄像头控制参数")
        print("3. 获取当前设置")
        print("4. 设置手动曝光")
        print("5. 设置自动曝光")
        print("6. 设置亮度")
        print("7. 设置对比度")
        print("8. 创建自定义launch文件")
        print("9. 快速设置(适合LED检测)")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-9): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("\n可用摄像头设备:")
            for i, device in enumerate(controller.available_devices):
                print(f"  {i}: {device}")
        
        elif choice in ['2', '3', '4', '5', '6', '7', '9']:
            # 选择设备
            print("\n选择摄像头设备:")
            for i, device in enumerate(controller.available_devices):
                print(f"  {i}: {device}")
            
            try:
                device_idx = int(input("请输入设备编号: "))
                if 0 <= device_idx < len(controller.available_devices):
                    device = controller.available_devices[device_idx]
                else:
                    print("无效的设备编号")
                    continue
            except ValueError:
                print("请输入有效的数字")
                continue
            
            if choice == '2':
                controller.list_camera_controls(device)
            elif choice == '3':
                controller.get_current_settings(device)
            elif choice == '4':
                try:
                    exposure = int(input("请输入曝光值 (1-5000): "))
                    controller.set_exposure_manual(device, exposure)
                except ValueError:
                    print("请输入有效的数字")
            elif choice == '5':
                controller.set_exposure_auto(device)
            elif choice == '6':
                try:
                    brightness = int(input("请输入亮度值 (0-255): "))
                    controller.set_brightness(device, brightness)
                except ValueError:
                    print("请输入有效的数字")
            elif choice == '7':
                try:
                    contrast = int(input("请输入对比度值 (0-255): "))
                    controller.set_contrast(device, contrast)
                except ValueError:
                    print("请输入有效的数字")
            elif choice == '9':
                # 快速设置适合LED检测的参数
                print("应用适合LED检测的设置...")
                controller.set_exposure_manual(device, 50)  # 较低曝光
                controller.set_brightness(device, 100)      # 中等亮度
                controller.set_contrast(device, 150)        # 较高对比度
                print("已应用LED检测优化设置")
        
        elif choice == '8':
            print("\n创建自定义launch文件")
            print("选择摄像头设备:")
            for i, device in enumerate(controller.available_devices):
                print(f"  {i}: {device}")
            
            try:
                device_idx = int(input("请输入设备编号: "))
                if 0 <= device_idx < len(controller.available_devices):
                    device = controller.available_devices[device_idx]
                    
                    exposure_mode = input("曝光模式 (auto/manual) [auto]: ").strip() or "auto"
                    exposure_value = int(input("曝光值 (1-5000) [100]: ") or "100")
                    brightness = int(input("亮度 (0-255) [128]: ") or "128")
                    contrast = int(input("对比度 (0-255) [128]: ") or "128")
                    
                    controller.create_custom_launch_file(device, exposure_mode, 
                                                       exposure_value, brightness, contrast)
                else:
                    print("无效的设备编号")
            except ValueError:
                print("请输入有效的数字")
        
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
