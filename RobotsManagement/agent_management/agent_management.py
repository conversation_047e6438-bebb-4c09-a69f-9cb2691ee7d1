import os
import time
import socket
import threading
import numpy as np
import json
import base64
import cmd

import urllib.request

import cv2
import rospy
from cv_bridge import CvBridge

from sensor_msgs.msg import Joy
from geometry_msgs.msg import PointStamped
from sensor_msgs.msg import Image
from paho.mqtt import client as mqtt_client
import pickle



broker = '10.0.2.66'  # mqtt代理服务器地址
port = 1883
keepalive = 60     # 与代理通信之间允许的最长时间段（以秒为单位）              

username = '00799df312a45f05'
password = 'DY6XhJ7Rki4UEX8Q7lNdWo9AiamInQ9AxufjnlH48r8vC'

cam_topic_list = []
pos_topic_list = []
ros_pub_dict = {}

class mqtt_client_thread():
    def __init__(self, broker, port, keepalive, client_id):
        super(mqtt_client_thread, self).__init__()
        self.broker = broker  # mqtt代理服务器地址
        self.port = port
        self.keepalive = keepalive 
        self.client_id = client_id
        self.client = self.connect_mqtt()
        self.client.on_message = self.mqtt_callback
        self.cv_bridge = CvBridge()
        self.agent_list = self.get_agent() 
        
        # 用于回传图像的显示设置，默认不翻转，不创建窗口
        self.flip = False  
        self.window = False
        
    def publish(self, topic, msg):
        result = self.client.publish(topic, msg)
        status = result[0]
        if status == 0:
            pass
            # print(f"Send `{msg}` to topic `{topic}`")
        else:
            print(f"Failed to send message to topic {topic}")     
             
    def mqtt_callback(self, client, userdata, msg):
        global ros_pub_dict
        '''订阅消息回调函数'''
        if msg.topic in cam_topic_list:
            _, _agent_id, _,_cam_id = msg.topic.split('/')
            img = cv2.imdecode(np.frombuffer(msg.payload, np.uint8), cv2.IMREAD_COLOR)
            #img = cv2.resize(img, (640,480))
            if self.flip is False:
                img = cv2.flip(img, -1)
            image_message = self.cv_bridge.cv2_to_imgmsg(img, "bgr8")
            image_message.header.stamp = rospy.Time.now()
            ros_pub_dict[msg.topic].publish(image_message)
            
            if self.window is True:
                cv2.imshow(f"{_agent_id} cam{_cam_id}", img)
                if cv2.waitKey(1):
                    return  
        if msg.topic in pos_topic_list:
            pos_msg = PointStamped()
            cmd_msg = json.loads(msg.payload.decode())
            pos_msg.header.seq = rospy.Time.now()#get_rostime().secs
            pos_msg.header.frame_id = "vicon"
            pos_msg.point.x = cmd_msg['x']
            pos_msg.point.y = cmd_msg['y']
            pos_msg.point.z = cmd_msg['z']
            ros_pub_dict[f'{msg.topic}'].publish(pos_msg)
            
    def get_agent(self):
        client_url = f'http://{broker}:18083/api/v5/clients'
        req = urllib.request.Request(client_url)
        req.add_header('Content-Type', 'application/json')

        auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
        req.add_header('Authorization', auth_header)

        with urllib.request.urlopen(req) as response:
            client_data = json.loads(response.read().decode())
        
        agent_list = []
        for client in client_data['data']:
            if '_robot' in client['clientid']:
                agent_list.append(f"/{client['clientid']}")
        return agent_list        
    
    def get_cam_subscribed(self):
        # return the cam topic subscribed by other terminal
        client_url = f'http://{broker}:18083/api/v5/clients'
        req = urllib.request.Request(client_url)
        req.add_header('Content-Type', 'application/json')

        auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
        req.add_header('Authorization', auth_header)

        with urllib.request.urlopen(req) as response:
            client_data = json.loads(response.read().decode())
        
        client_list = []
        for client in client_data['data']:
            if '_management' in client['clientid']:
                client_list.append(client['clientid'])

        cam_subscribed_set = []
        for client in client_list:
            sub_url = f'http://{broker}:18083/api/v5/clients/{client}/subscriptions'
            req = urllib.request.Request(sub_url)
            req.add_header('Content-Type', 'application/json')

            auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
            req.add_header('Authorization', auth_header)

            with urllib.request.urlopen(req) as response:
                sub_data = json.loads(response.read().decode())
            for cam in sub_data:
                cam_topic = cam['topic']
                if cam_topic not in cam_subscribed_set:
                    cam_subscribed_set.append(cam_topic)

        return cam_subscribed_set
    
    def set_camera_subscribe(self, cmd_agent_id, cmd_cam_id = None, enable = True, flip=False, window=False):
        self.agent_list = self.get_agent() 
        if (cmd_cam_id == None) or (enable == False):
            for i in range(4):
                cmd_cam_id = i
                cmd_msg = {
                    'cmd_type': 'cam',
                    'args_length':2,
                    'args':{'0':str(cmd_cam_id),
                            '1':enable}
                }
                self.publish(self.agent_list[cmd_agent_id] + '/cmd', json.dumps(cmd_msg))
                cam_topic = self.agent_list[cmd_agent_id] + f'/cam/{cmd_cam_id}'
                cam_topic_list.append(cam_topic)
                if enable == False:                
                    self.client.unsubscribe(cam_topic)
                    time.sleep(0.1)
                    if cam_topic in ros_pub_dict:
                        ros_pub_dict[cam_topic].unregister()
                else:
                    self.client.subscribe(cam_topic)
                    ros_pub_dict[cam_topic] = rospy.Publisher(f"{cam_topic}/image_raw",Image, queue_size=5)
                time.sleep(0.1)
        else:
            cmd_msg = {
                'cmd_type': 'cam',
                'args_length':2,
                'args':{'0':str(cmd_cam_id),
                        '1':True}
            }
            self.publish(self.agent_list[cmd_agent_id] + '/cmd', json.dumps(cmd_msg))
            cam_topic = self.agent_list[cmd_agent_id] + f'/cam/{cmd_cam_id}'
            cam_topic_list.append(cam_topic)
            self.client.subscribe(cam_topic)
            ros_pub_dict[cam_topic] = rospy.Publisher(f"{cam_topic}/image_raw",Image, queue_size=5)
            time.sleep(0.1)
    def set_position_subscribe(self, cmd_agent_id, enable = True):
        self.agent_list = self.get_agent() 
        cmd_msg = {
            'cmd_type': 'pos',
            'args_length':1,
            'args':{'0':enable}
        }     
        global pos_topic_list
        if enable == True:
            self.publish(self.agent_list[cmd_agent_id] + '/cmd', json.dumps(cmd_msg))
            pos_topic = f'{self.agent_list[cmd_agent_id]}/pos'
            pos_topic_list.append(pos_topic)
            self.client.subscribe(pos_topic) 
            ros_pub_dict[pos_topic] = rospy.Publisher(f"/vicon/pos{self.agent_list[cmd_agent_id]}",PointStamped,queue_size=10)
        else:
            self.publish('/Broadcast/cmd', json.dumps(cmd_msg))
            for topic in pos_topic_list:
                self.client.unsubscribe(topic)
                ros_pub_dict[topic].unregister()
            pos_topic_list = []
    
    def set_pcontrol(self, cmd_agent_id, x, y):
        self.agent_list = self.get_agent() 
        cmd_msg = {
            'cmd_type': 'pos_ctrl',
            'args_length':2,
            'args':{'0': x,
                    '1': y}
        }
        self.publish(self.agent_list[cmd_agent_id] + '/cmd', json.dumps(cmd_msg))
             
    def connect_mqtt(self):
        '''连接mqtt代理服务器'''
        def on_connect(client, userdata, flags, rc):
            '''连接回调函数'''
            # 响应状态码为0表示连接成功
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print("Failed to connect, return code %d", rc)
        # 连接mqtt代理服务器，并获取连接引用
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client    
      
    def run(self):
        self.client.loop_forever()   
               
class user_cmd_input(cmd.Cmd):
    intro = ">>Welcome to the CLI. Type 'help' to list commands."
    prompt = ">>"
    def __init__(self, client, ros):
        super(user_cmd_input, self).__init__()
        self.client = client
        self.ros = ros
        self.agent_list = self.client.get_agent() 

    def do_ls(self, args):
        """\n 查看在线设备\n-t:显示订阅话题\n-l:显示设备功能\n"""
        if not args :
            self.agent_list = self.client.get_agent() 
            for i, agent in enumerate(self.agent_list):
                print(f"{i}.{agent[1:]}")
            return 
        try:
            args = args.split()
            if (args[0] == "-t")and(len(args)==1):
                print(self.client.get_cam_subscribed())
        except ValueError:
            print("Error input")
            return          
    def do_camera(self, args):
        """\n摄像头相关操作\n-a: agent编号\n-c: 相机编号\n-x: 关闭所有相机显示窗口及话题\n"""
        # 返回键入列表     
        if not args :
            print("Error input")
            return  
        try:
            args = args.split()
            # 关闭所有设备
            if (args[0] == "-x")and(len(args)==1):
                self.agent_list = self.client.get_agent() 
                for cmd_agent_id in range(len(self.agent_list)):
                    client.set_camera_subscribe(cmd_agent_id, enable = False)
                print("close all camera")
                time.sleep(0.1)
                cv2.destroyAllWindows()
            # 打开一个agent的所有镜头
            elif (args[0] == "-a")and(len(args)==2):
                cmd_agent_id = int(args[1])
                client.set_camera_subscribe(cmd_agent_id)
            # 打开一个agent的指定镜头
            elif (args[0] == "-a")and(args[2] == "-c")and(len(args)==4):
                cmd_agent_id = int(args[1])
                cmd_cam_id = int(args[3])
                client.set_camera_subscribe(cmd_agent_id, cmd_cam_id)
            else:
                print("Error input")
        except ValueError:
            print("Error input")
            return 
        except IndexError:
            print("Agent not find")
            return 
    def do_position(self, args):
        if not args :
            print("Error input")
            return  
        try:
            args = args.split()
            # 关闭所有设备
            if (args[0] == "-a")and(len(args)==2):
                cmd_agent_id = int(args[1])
                self.client.set_position_subscribe(cmd_agent_id)
            elif (args[0] == "-x")and(len(args)==1):
                self.client.set_position_subscribe(cmd_agent_id=None, enable=False)
            else:
               print("Input error") 
        except ValueError:
            print("Input error")
            return 
        except IndexError:
            print("Agent not find")
            return 
    def do_pcontrol(self, args):
        if not args:
            #print(f"current goal: ({self.ros_node.x_goal},{self.ros_node.y_goal})" )
            pass
        else:
            try:
                args = args.split()
                # print(args)
                if (args[0] == "-a")and(len(args)==4):
                    self.client.set_pcontrol(int(args[1]), float(args[2]), float(args[3]))
                #self.ros_node.x_goal = float(args[0])
                #self.ros_node.y_goal = float(args[1])
                if (args[0] == "-f") and (len(args) == 3):
                    car_id = int(args[1])
                    file_path = args[2]
                    with open(file_path, "rb") as f:
                        traj = pickle.load(f)
                    for each in traj:
                        self.client.set_pcontrol(car_id, each[0], each[1])
                        if each[2] == "0":
                            self.ros.set_led_show(-1, 0, 0x00, 0x00, 0x00)
                            self.ros.set_led_show(-1, 1, 0x00, 0x00, 0x00)
                        elif each[2] == "1":
                            self.ros.set_led_show(-1, 0, 0x3f, 0x3f, 0x3f)
                            self.ros.set_led_show(-1, 1, 0x3f, 0x3f, 0x3f)
                        elif each[2] == "2":
                            self.ros.set_led_show(-1, 0, 0x3f00, 0x3f00, 0x3f00)
                            self.ros.set_led_show(-1, 1, 0x3f00, 0x3f00, 0x3f00)
                        elif each[2] == "3":
                            self.ros.set_led_show(-1, 0, 0x3f0000, 0x3f0000, 0x3f0000)
                            self.ros.set_led_show(-1, 1, 0x3f0000, 0x3f0000, 0x3f0000)
                        elif each[2] == "4":
                            self.ros.set_led_show(-1, 0, 0x3f3f00, 0x3f3f000, 0x3f3f000)
                            self.ros.set_led_show(-1, 1, 0x3f3f00, 0x3f3f000, 0x3f3f000)
                        print(each)
                        time.sleep(0.2)
                    
                
                
            except ValueError:
                print("Error input")
                return  
    def do_rgb(self, args):
        if not args:
            #print(f"current goal: ({self.ros_node.x_goal},{self.ros_node.y_goal})" )
            pass
        else:
            try:
                args = args.split()
                print(args)
                if (args[0] == "-a")and(len(args) == 5):
                    car_id = int(args[1])
                    rgb_data = args[2:]
                    self.ros.set_led_show(car_id, 0, int(rgb_data[0], 16), int(rgb_data[1], 16), int(rgb_data[2], 16))
                    self.ros.set_led_show(car_id, 1, int(rgb_data[0], 16), int(rgb_data[1], 16), int(rgb_data[2], 16))
            except ValueError:
                print("Error input")     
                 
    def do_motion(self, args):
        """\n 发送运动控制指令\n-a:指定设备\n-A:群发\n"""
        
    def do_quit(self, arg):
        """Exit the CLI."""
        print("Exiting...")
        os._exit(0)
            
    #空发送时执行命令
    def emptyline(self):
        return
    #找不到指令
    def default(self, line):
        self.stdout.write('%s:not found\n'%line)
        
class ros_node_thread():
    def __init__(self, client):
        self.client = client
        self.agent_id = 0
        self.vel_level = 0.5
        self.vel_x = 0
        self.vel_y = 0
        self.vel_z = 0
        self.control_enable = 0
        self.agent_list = self.client.get_agent() 
        self.last_buttons = []
        for i in range(10):
            self.last_buttons.append(0)
        self.last_axes = 0
        try:
            rospy.init_node('agent_management', anonymous=True)
            rospy.Subscriber("/joy", Joy, self.handle_joy)
            print("Connected to ROS OK!")
        except:
            print("Connected to ROS Failed!")
        self.set_led_show(-1, 0)
        self.set_led_show(-1, 1) 
        
    def limit(self, value, lower, upper):
        if value < lower:
            return lower
        elif value > upper:
            return upper
        else:
            return value            
    def set_led_show(self, agent_id, up_down, data1 = 0, data2 = 0, data3 = 0):   
        if up_down == 0:
            ledup_msg = {
                    'cmd_type': 'ledup',
                    'args_length': 6,
                    'args':{
                            '0':data1,
                            '1':14,
                            '2':data2,
                            '3':14,
                            '4':data3,
                            '5':14,
                            }
                }  
            if agent_id == -1:
                ledup_msg = json.dumps(ledup_msg)
                self.client.publish('/Broadcast/cmd' , ledup_msg) 
            else:
                ledup_msg = json.dumps(ledup_msg)
                self.client.publish(self.agent_list[agent_id] + '/cmd' , ledup_msg)  
        elif up_down == 1:
            leddown_msg = {
                'cmd_type': 'leddown',
                'args_length': 6,
                'args':{
                        '0':data1,
                        '1':30,
                        '2':data2,
                        '3':30,
                        '4':data3,
                        '5':29,
                        }
                
            }
            if agent_id == -1:
                leddown_msg = json.dumps(leddown_msg)
                self.client.publish('/Broadcast/cmd' , leddown_msg) 
            else:
                leddown_msg = json.dumps(leddown_msg)
                self.client.publish(self.agent_list[agent_id] + '/cmd' , leddown_msg)  
        
    def handle_joy(self, msg):
        if len(self.agent_list) == 0:
            return
        else:
            pass
        if (msg.axes[-1] == 1) and (self.last_axes == 0):
            self.vel_level += 0.1
            print("\n max vel is : %s"%(self.vel_level * 0.85))
            self.last_axes = msg.axes[-1]
        elif (msg.axes[-1] == -1) and (self.last_axes == 0):
            self.vel_level -= 0.1
            print("\n max vel is : %s"%(self.vel_level * 0.85))
            self.last_axes = msg.axes[-1]
        else:
            self.last_axes = msg.axes[-1]
        self.vel_level = self.limit(self.vel_level, 0, 3)
        if (msg.buttons[3] == 1) and (self.last_buttons[3] == 0):
            # botton Y
            # control all agent
            self.last_buttons[3] = msg.buttons[3]
            self.control_enable = not self.control_enable
            if self.control_enable == True:
                self.set_led_show(-1, 0, 0x3f0000, 0x003f00, 0x3f)
                self.set_led_show(-1, 1, 0x3f0000, 0x003f00, 0x3f)
                self.set_led_show(self.agent_id, 0, 0x3f, 0x3f, 0x3f)
                self.set_led_show(self.agent_id, 1, 0x3f, 0x3f, 0x3f)  
            elif self.control_enable == False:
                self.set_led_show(-1, 0)
                self.set_led_show(-1, 1) 
        elif(msg.buttons[3] == 0) and (self.last_buttons[3] == 1):
            self.last_buttons[3] = msg.buttons[3]
        else:
            self.last_buttons[3] = msg.buttons[3]
            
            
        if (msg.buttons[0] == 1) and (self.last_buttons[0] == 0):
            # botton A
            # control all agent
            self.last_buttons[0] = msg.buttons[0]
            self.agent_id =-1
            if self.control_enable == True:
                self.set_led_show(-1, 0, 0x3f, 0x3f, 0x3f)
                self.set_led_show(-1, 1, 0x3f, 0x3f, 0x3f) 
        elif(msg.buttons[0] == 0) and (self.last_buttons[0] == 1):
            self.last_buttons[0] = msg.buttons[0]
        else:
            self.last_buttons[0] = msg.buttons[0]
        if (msg.buttons[2] == 1) and (self.last_buttons[2] == 0):
            # botton x
            # the msg to control top led 
            # send the message change the first agent to be colorful
            # represent the agent is not controled by joystick any more
            self.agent_list = self.client.get_agent()       
            self.agent_id += 1
            self.agent_id %= len(self.agent_list) 
            self.last_buttons[2] = msg.buttons[2]
            if self.control_enable == True:
                self.set_led_show(-1, 0, 0x3f0000, 0x003f00, 0x3f)
                self.set_led_show(-1, 1, 0x3f0000, 0x003f00, 0x3f)
                self.set_led_show(self.agent_id, 0, 0x3f, 0x3f, 0x3f)
                self.set_led_show(self.agent_id, 1, 0x3f, 0x3f, 0x3f)          

        elif(msg.buttons[2] == 0) a我觉nd (self.last_buttons[2] == 1):
            self.last_buttons[2] = msg.buttons[2]
        else:
            self.last_buttons[2] = msg.buttons[2]  
                     
        self.vel_x = self.deadband(-msg.axes[4], 0.15) * self.vel_level
        self.vel_y = self.deadband(-msg.axes[3], 0.15) * self.vel_level
        self.vel_z = self.deadband(msg.axes[0], 0.15) * 6.28 * self.vel_level
        
            
    def deadband(self, value, deadband):
        if abs(value) < deadband:
            return 0
        elif value > 0:
            return value - deadband
        elif value < 0:
            return value + deadband
        else:
            return value   

            
       
    def vel_control_machine(self):
        if(self.control_enable != True):
            return
        motion_msg = {
            "x": self.vel_x,
            "y": self.vel_y,
            "theta": self.vel_z,
        }
        motion_msg = json.dumps(motion_msg)
        # 单个控制
        if self.agent_id != -1:
            self.client.publish(self.agent_list[self.agent_id] + '/motion' , motion_msg) 
        # 全局控制
        else:
            cmd_msg = {
                'cmd_type': 'motion',
                'args_length': 3,
                'args':{'0': self.vel_x, 
                        '1': self.vel_y,
                        '2': self.vel_z,
                        }
            }
            cmd_msg = json.dumps(cmd_msg)
            self.client.publish('/Broadcast/cmd', cmd_msg)
    def run(self):
        rospy.spin()
    

if __name__ =="__main__":
   
    # 启动MQTT客户端的线程
    mqtt_client_instance = mqtt_client_thread(broker=broker, 
                                            port=port, 
                                            keepalive=keepalive, 
                                            client_id=f'{socket.gethostname()}_management'
                                           )
    mqtt_thread = threading.Thread(target=mqtt_client_instance.run)
    mqtt_thread.start()
    client = mqtt_client_instance
    # 启动Cli线程 
    time.sleep(1)

    ros_instance = ros_node_thread(client=client)
    ros_thread = threading.Thread(target=ros_instance.run)
    ros_thread.start()

    time.sleep(1)
    user_cmd_instance = user_cmd_input(client=client, ros = ros_instance)
    cli_thread = threading.Thread(target=user_cmd_instance.cmdloop)
    cli_thread.start()
    
    ros_hz = 100
    rate = rospy.Rate(ros_hz)
    while not rospy.is_shutdown():
        ros_instance.vel_control_machine()
        rate.sleep()
 
    


